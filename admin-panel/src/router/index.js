import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('../layout/Layout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '控制面板' }
      },
      {
        path: 'videos',
        name: 'VideoManage',
        component: () => import('../views/video/List.vue'),
        meta: { title: '视频管理' }
      },
      {
        path: 'videos/create',
        name: 'VideoCreate',
        component: () => import('../views/video/Form.vue'),
        meta: { title: '添加视频' }
      },
      {
        path: 'videos/edit/:id',
        name: 'VideoEdit',
        component: () => import('../views/video/Form.vue'),
        meta: { title: '编辑视频' }
      },
      {
        path: 'import',
        name: 'DataImport',
        component: () => import('../views/import/Index.vue'),
        meta: { title: '数据导入' }
      },
      {
        path: 'stars',
        name: 'StarManage',
        component: () => import('../views/star/List.vue'),
        meta: { title: '演员管理' }
      },
      {
        path: 'stars/create',
        name: 'StarCreate',
        component: () => import('../views/star/Form.vue'),
        meta: { title: '添加演员' }
      },
      {
        path: 'stars/edit/:id',
        name: 'StarEdit',
        component: () => import('../views/star/Form.vue'),
        meta: { title: '编辑演员' }
      },
      {
        path: 'stars/detail/:id',
        name: 'StarDetail',
        component: () => import('../views/star/Detail.vue'),
        meta: { title: '演员详情' }
      },
      {
        path: 'users',
        name: 'UserManage',
        component: () => import('../views/user/List.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'users/create',
        name: 'UserCreate',
        component: () => import('../views/user/Form.vue'),
        meta: { title: '添加用户' }
      },
      {
        path: 'users/edit/:id',
        name: 'UserEdit',
        component: () => import('../views/user/Form.vue'),
        meta: { title: '编辑用户' }
      },
      {
        path: 'users/detail/:id',
        name: 'UserDetail',
        component: () => import('../views/user/Detail.vue'),
        meta: { title: '用户详情' }
      },
      {
        path: 'stats',
        name: 'StatsIndex',
        component: () => import('../views/stats/Index.vue'),
        meta: { title: '数据统计' }
      },
      {
        path: 'genres',
        name: 'GenreManage',
        component: () => import('../views/genre/List.vue'),
        meta: { title: '类型管理' }
      },
      {
        path: 'genres/create',
        name: 'GenreCreate',
        component: () => import('../views/genre/Form.vue'),
        meta: { title: '添加类型' }
      },
      {
        path: 'genres/edit/:id',
        name: 'GenreEdit',
        component: () => import('../views/genre/Form.vue'),
        meta: { title: '编辑类型' }
      },
      {
        path: 'genres/detail/:id',
        name: 'GenreDetail',
        component: () => import('../views/genre/Detail.vue'),
        meta: { title: '类型详情' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('admin_token')
  
  // 如果从登录页面来，说明已经登录成功，直接放行
  if (from.path === '/login') {
    next()
    return
  }
  
  if (to.meta.requiresAuth === false) {
    // 不需要认证的页面
    next()
  } else if (to.meta.requiresAuth !== false && !token) {
    // 需要认证但没有token
    next('/login')
  } else {
    // 需要认证且有token
    next()
  }
})

export default router 