package config

import (
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
)

type Config struct {
	// 服务器配置
	Port     string
	GinMode  string
	LogLevel string

	// Node.js API服务器配置
	APIServerURL   string
	APIServerToken string

	// Redis配置
	RedisURL      string
	RedisPassword string
	RedisDB       int

	// 文件存储配置
	TempDir        string
	MaxDiskUsage   int
	CleanupEnabled bool

	// 并发配置
	MaxConcurrentDownloads int
	MaxConcurrentProcesses int
	MaxConcurrentUploads   int
	WorkerPoolSize         int

	// CDN配置
	CDNProvider string

	// Cloudflare R2配置
	R2Endpoint        string
	R2AccessKeyID     string
	R2SecretAccessKey string
	R2Bucket          string
	R2PublicDomain    string

	// 阿里云OSS配置
	AliyunOSSEndpoint  string
	AliyunOSSAccessKey string
	AliyunOSSSecretKey string
	AliyunOSSBucket    string

	// AWS S3配置
	AWSRegion          string
	AWSAccessKeyID     string
	AWSSecretAccessKey string
	AWSS3Bucket        string

	// 安全配置
	AllowedIPs []string
	JWTSecret  string
	HMACSecret string

	// 监控配置
	PrometheusEnabled   bool
	PrometheusPort      string
	HealthCheckInterval string

	// 视频处理配置
	DefaultVideoQualities []VideoQuality
	WatermarkConfig       WatermarkConfig

	// 处理模式配置
	EnableRealProcessing bool
	EnableRealWatermark  bool
	EnableRealHLS        bool
	EnableRealCDNUpload  bool
}

type VideoQuality struct {
	Resolution string `json:"resolution"`
	Bitrate    string `json:"bitrate"`
	Scale      string `json:"scale"`
}

type WatermarkConfig struct {
	DefaultText     string  `json:"defaultText"`
	DefaultPosition string  `json:"defaultPosition"`
	DefaultOpacity  float64 `json:"defaultOpacity"`
	DefaultFontSize int     `json:"defaultFontSize"`
	DefaultColor    string  `json:"defaultColor"`
}

func Load() *Config {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		logrus.Warn("未找到.env文件，使用环境变量")
	}

	config := &Config{
		// 服务器配置
		Port:     getEnv("PORT", "8080"),
		GinMode:  getEnv("GIN_MODE", "release"),
		LogLevel: getEnv("LOG_LEVEL", "info"),

		// Node.js API服务器配置
		APIServerURL:   getEnv("API_SERVER_URL", "http://localhost:4000"),
		APIServerToken: getEnv("API_SERVER_TOKEN", ""),

		// Redis配置
		RedisURL:      getEnv("REDIS_URL", "redis://localhost:6379"),
		RedisPassword: getEnv("REDIS_PASSWORD", ""),
		RedisDB:       getEnvAsInt("REDIS_DB", 0),

		// 文件存储配置
		TempDir:        getEnv("TEMP_DIR", "/tmp/javflix-processing"),
		MaxDiskUsage:   getEnvAsInt("MAX_DISK_USAGE", 80),
		CleanupEnabled: getEnvAsBool("CLEANUP_ENABLED", true),

		// 并发配置
		MaxConcurrentDownloads: getEnvAsInt("MAX_CONCURRENT_DOWNLOADS", 4),
		MaxConcurrentProcesses: getEnvAsInt("MAX_CONCURRENT_PROCESSES", 2),
		MaxConcurrentUploads:   getEnvAsInt("MAX_CONCURRENT_UPLOADS", 6),
		WorkerPoolSize:         getEnvAsInt("WORKER_POOL_SIZE", 8),

		// CDN配置
		CDNProvider: getEnv("CDN_PROVIDER", "cloudflare_r2"),

		// Cloudflare R2配置
		R2Endpoint:        getEnv("R2_ENDPOINT", "https://3b6dc8a552f1f639304d79328d3a3166.r2.cloudflarestorage.com"),
		R2AccessKeyID:     getEnv("R2_ACCESS_KEY_ID", ""),
		R2SecretAccessKey: getEnv("R2_SECRET_ACCESS_KEY", ""),
		R2Bucket:          getEnv("R2_BUCKET", "javflix-videos"),
		R2PublicDomain:    getEnv("R2_PUBLIC_DOMAIN", "https://cdn.javflix.tv"),

		// 阿里云OSS配置
		AliyunOSSEndpoint:  getEnv("ALIYUN_OSS_ENDPOINT", ""),
		AliyunOSSAccessKey: getEnv("ALIYUN_OSS_ACCESS_KEY", ""),
		AliyunOSSSecretKey: getEnv("ALIYUN_OSS_SECRET_KEY", ""),
		AliyunOSSBucket:    getEnv("ALIYUN_OSS_BUCKET", ""),

		// AWS S3配置
		AWSRegion:          getEnv("AWS_REGION", "us-west-2"),
		AWSAccessKeyID:     getEnv("AWS_ACCESS_KEY_ID", ""),
		AWSSecretAccessKey: getEnv("AWS_SECRET_ACCESS_KEY", ""),
		AWSS3Bucket:        getEnv("AWS_S3_BUCKET", ""),

		// 安全配置
		AllowedIPs: strings.Split(getEnv("ALLOWED_IPS", ""), ","),
		JWTSecret:  getEnv("JWT_SECRET", ""),
		HMACSecret: getEnv("HMAC_SECRET", ""),

		// 监控配置
		PrometheusEnabled:   getEnvAsBool("PROMETHEUS_ENABLED", true),
		PrometheusPort:      getEnv("PROMETHEUS_PORT", "9090"),
		HealthCheckInterval: getEnv("HEALTH_CHECK_INTERVAL", "30s"),

		// 默认视频质量配置
		DefaultVideoQualities: []VideoQuality{
			{Resolution: "1080p", Bitrate: "5000k", Scale: "1920:1080"},
			{Resolution: "720p", Bitrate: "3000k", Scale: "1280:720"},
			{Resolution: "480p", Bitrate: "1500k", Scale: "854:480"},
		},

		// 默认水印配置
		WatermarkConfig: WatermarkConfig{
			DefaultText:     "JAVFLIX.TV",
			DefaultPosition: "bottom-right",
			DefaultOpacity:  0.8,
			DefaultFontSize: 24,
			DefaultColor:    "white",
		},

		// 处理模式配置
		EnableRealProcessing: getEnvAsBool("ENABLE_REAL_PROCESSING", true),
		EnableRealWatermark:  getEnvAsBool("ENABLE_REAL_WATERMARK", true),
		EnableRealHLS:        getEnvAsBool("ENABLE_REAL_HLS", true),
		EnableRealCDNUpload:  getEnvAsBool("ENABLE_REAL_CDN_UPLOAD", true),
	}

	// 验证必需的配置
	if config.APIServerToken == "" {
		logrus.Fatal("API_SERVER_TOKEN 是必需的")
	}

	if config.JWTSecret == "" {
		logrus.Fatal("JWT_SECRET 是必需的")
	}

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
