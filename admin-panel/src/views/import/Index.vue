<template>
  <div class="import-container">
    <div class="page-header">
      <h2>数据导入</h2>
    </div>

    <el-card shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>JavBus数据源</span>
        </div>
      </template>

      <el-form :model="importForm" label-width="120px">
        <el-form-item label="采集页数">
          <el-input-number v-model="importForm.page" :min="1" :max="100" />
          <span class="form-tip">（从第1页开始采集到第N页，如设置4则采集1-4页）</span>
        </el-form-item>
        <el-form-item label="每页数量">
          <el-input-number v-model="importForm.count" :min="1" :max="50" />
          <span class="form-tip">（每页最多采集数量，建议不超过20个）</span>
        </el-form-item>
        <el-form-item label="磁力链接">
          <el-radio-group v-model="importForm.magnet">
            <el-radio value="exist">仅包含磁力链接</el-radio>
            <el-radio value="all">全部影片</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="影片类型">
          <el-radio-group v-model="importForm.type">
            <el-radio value="normal">一般影片</el-radio>
            <el-radio value="actress">女优影片</el-radio>
            <el-radio value="uncensored">无码影片</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleImport" :loading="importing">
            开始导入
          </el-button>
          <el-button @click="handleSearch">预览影片列表</el-button>
          <el-button type="warning" @click="handleUpdateReleaseDate" :loading="updatingReleaseDate">
            更新发行日期
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- JavBus影片列表预览 -->
    <el-card v-if="showPreview" shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>影片列表预览</span>
          <div>
            <el-button type="success" @click="handleImportSelected" :disabled="selectedMovies.length === 0" :loading="importing">
              导入选中 ({{ selectedMovies.length }})
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="moviesList" 
        v-loading="loading" 
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="影片ID" width="120" />
        <el-table-column label="封面" width="120">
          <template #default="scope">
            <el-image
              style="width: 75px; height: 100px"
              :src="scope.row.img"
              fit="cover"
              :preview-src-list="[scope.row.img]"
            >
              <template #error>
                <div class="image-error">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="date" label="发行日期" width="120" />
        <el-table-column label="标签" width="150">
          <template #default="scope">
            <div v-if="scope.row.tags && scope.row.tags.length">
              <el-tag v-for="tag in scope.row.tags" :key="tag" size="small" style="margin-right: 5px">
                {{ tag }}
              </el-tag>
            </div>
            <span v-else>无标签</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button 
              size="small"
              type="primary"
              @click="handleViewDetail(scope.row.id)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="100"
          :current-page="importForm.page"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 导入结果 -->
    <el-card v-if="importedMovies.length > 0" shadow="never">
      <template #header>
        <div class="card-header">
          <span>导入结果</span>
        </div>
      </template>

      <!-- 导入状态信息 -->
      <div v-if="importStatus && importStatus.isImporting" class="import-status">
        <el-progress 
          :percentage="importStatus.progress" 
          :format="format => `${format}%`" 
          status="success"
        />
        <div class="import-stats">
          <p>正在导入: {{ importStatus.currentMovieId || '准备中...' }}</p>
          <p>进度: {{ importStatus.processedMovies }} / {{ importStatus.totalMovies }}</p>
          <p>成功: {{ importStatus.successfulMovies }}</p>
          <p>失败: {{ importStatus.failedMovies }}</p>
          <div v-if="importStatus.lastError" class="error-message">
            <p>最近错误: {{ importStatus.lastError }}</p>
          </div>
        </div>
      </div>

      <el-table :data="importedMovies" style="width: 100%">
        <el-table-column prop="id" label="影片ID" width="120" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消息" />
      </el-table>
    </el-card>

    <!-- 影片详情对话框 -->
    <el-dialog 
      title="影片详情" 
      v-model="dialogVisible" 
      width="70%"
      destroy-on-close
    >
      <div v-loading="loadingDetail">
        <div v-if="movieDetail" class="movie-detail">
          <div class="movie-detail-header">
            <div class="movie-poster">
              <el-image
                style="width: 200px; height: 280px"
                :src="movieDetail.img"
                fit="cover"
              />
            </div>
            <div class="movie-info">
              <h3>{{ movieDetail.title }}</h3>
              <div class="movie-meta">
                <p><strong>影片ID:</strong> {{ movieDetail.id }}</p>
                <p><strong>发行日期:</strong> {{ movieDetail.date }}</p>
                <p v-if="movieDetail.length"><strong>时长:</strong> {{ movieDetail.length }}</p>
                <p v-if="movieDetail.director?.name"><strong>导演:</strong> {{ movieDetail.director.name }}</p>
                <p v-if="movieDetail.maker?.name"><strong>制作商:</strong> {{ movieDetail.maker.name }}</p>
                <p v-if="movieDetail.label?.name"><strong>发行商:</strong> {{ movieDetail.label.name }}</p>
              </div>
            </div>
          </div>

          <div class="movie-section" v-if="movieDetail.genres && movieDetail.genres.length">
            <h4>类型</h4>
            <div class="movie-tags">
              <el-tag 
                v-for="genre in movieDetail.genres" 
                :key="genre.id"
                style="margin-right: 10px; margin-bottom: 10px;"
              >
                {{ genre.name }}
              </el-tag>
            </div>
          </div>

          <div class="movie-section" v-if="movieDetail.stars && movieDetail.stars.length">
            <h4>演员</h4>
            <div class="movie-stars">
              <div v-for="star in movieDetail.stars" :key="star.id" class="star-item">
                <el-avatar :size="80" :src="star.img" />
                <div class="star-name">{{ star.name }}</div>
              </div>
            </div>
          </div>

          <div class="movie-section" v-if="movieDetail.samples && movieDetail.samples.length">
            <h4>样品图像</h4>
            <div class="movie-samples">
              <el-image 
                v-for="(sample, index) in movieDetail.samples" 
                :key="index"
                style="width: 180px; height: 120px; margin-right: 10px; margin-bottom: 10px;"
                :src="sample.img"
                fit="cover"
                :preview-src-list="movieDetail.samples.map(s => s.img)"
              />
            </div>
          </div>
        </div>
        <div v-else-if="!loadingDetail" class="empty-data">
          暂无详细数据
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleImportSingle">导入该影片</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getJavBusMovies, 
  getJavBusMovieDetail,
  saveMovies,
  importMoviesFromJavBus,
  downloadMovieImages,
  getImportStatus
} from '../../api/import'

export default {
  name: 'DataImport',
  setup() {
    const loading = ref(false)
    const importing = ref(false)
    const loadingDetail = ref(false)
    const updatingReleaseDate = ref(false)
    const dialogVisible = ref(false)
    const showPreview = ref(false)
    const moviesList = ref([])
    const movieDetail = ref(null)
    const importedMovies = ref([])
    const selectedMovies = ref([])
    const importStatus = ref(null)
    const statusTimer = ref(null)
    
    const importForm = reactive({
      page: 1,
      count: 10,
      magnet: 'exist',
      type: 'normal'
    })
    
    // 搜索影片列表
    const handleSearch = async () => {
      loading.value = true
      showPreview.value = true
      try {
        const params = {
          page: importForm.page,
          magnet: importForm.magnet,
          type: importForm.type
        }
        
        const res = await getJavBusMovies(params)
        moviesList.value = res.movies || []
      } catch (error) {
        console.error('获取影片列表失败:', error)
        ElMessage.error('获取影片列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 页码变化
    const handlePageChange = (page) => {
      importForm.page = page
      handleSearch()
    }
    
    // 选择变化
    const handleSelectionChange = (selection) => {
      selectedMovies.value = selection
    }
    
    // 查看影片详情
    const handleViewDetail = async (id) => {
      dialogVisible.value = true
      loadingDetail.value = true
      movieDetail.value = null
      
      try {
        const res = await getJavBusMovieDetail(id)
        movieDetail.value = res
      } catch (error) {
        console.error('获取影片详情失败:', error)
        ElMessage.error('获取影片详情失败')
      } finally {
        loadingDetail.value = false
      }
    }
    
    // 开始轮询导入状态
    const startPollingStatus = () => {
      // 清除可能存在的旧定时器
      if (statusTimer.value) {
        clearInterval(statusTimer.value)
      }
      
      // 立即查询一次
      checkImportStatus()
      
      // 设置定时查询
      statusTimer.value = setInterval(() => {
        checkImportStatus()
      }, 5000) // 每5秒查询一次
    }
    
    // 停止轮询
    const stopPollingStatus = () => {
      if (statusTimer.value) {
        clearInterval(statusTimer.value)
        statusTimer.value = null
      }
    }
    
    // 检查导入状态
    const checkImportStatus = async () => {
      try {
        const status = await getImportStatus()
        importStatus.value = status
        
        // 如果导入已完成，停止轮询
        if (!status.isImporting) {
          stopPollingStatus()
        }
      } catch (error) {
        console.error('获取导入状态失败:', error)
      }
    }
    
    // 在组件卸载时清除定时器
    onMounted(() => {
      checkImportStatus() // 初始检查
    })
    
    onUnmounted(() => {
      stopPollingStatus()
    })
    
    // 导入全部
    const handleImport = async () => {
      importing.value = true
      importedMovies.value = []
      
      try {
        const response = await importMoviesFromJavBus({
          page: importForm.page,
          count: importForm.count,
          magnet: importForm.magnet,
          type: importForm.type,
          downloadImages: true
        })
        
        // 后端返回的是异步任务状态，不是结果数组
        if (response && response.taskId) {
          ElMessage.success(`成功提交导入任务，共${response.totalMovies}部影片，正在后台处理...`)
          
          // 开始轮询导入状态
          startPollingStatus()
          
          importedMovies.value = [{
            id: 'task-' + response.taskId,
            title: '批量导入任务',
            status: 'success',
            message: `已提交${response.totalMovies}部影片的导入任务`
          }]
        } else {
          ElMessage.warning('提交导入任务成功，但未收到任务ID')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败: ' + (error.message || '未知错误'))
      } finally {
        importing.value = false
      }
    }
    
    // 导入选中的影片
    const handleImportSelected = async () => {
      if (selectedMovies.value.length === 0) {
        ElMessage.warning('请选择要导入的影片')
        return
      }
      
      importing.value = true
      importedMovies.value = []
      
      try {
        // 获取选中影片的详情
        const detailedMovies = []
        for (const movie of selectedMovies.value) {
          try {
            ElMessage.info(`正在获取影片 ${movie.id} 详情`)
            const detail = await getJavBusMovieDetail(movie.id)

            // 获取磁力链接
            ElMessage.info(`正在获取影片 ${movie.id} 磁力链接`)
            try {
              const magnetsResponse = await fetch(`http://localhost:3000/api/magnets/${movie.id}?gid=${detail.gid}&uc=${detail.uc}`)
              if (magnetsResponse.ok) {
                const magnets = await magnetsResponse.json()
                detail.magnets = magnets || []
                console.log(`影片 ${movie.id} 获取到 ${detail.magnets.length} 个磁力链接`)
              } else {
                console.warn(`获取影片 ${movie.id} 磁力链接失败`)
                detail.magnets = []
              }
            } catch (magnetError) {
              console.error(`获取影片 ${movie.id} 磁力链接出错:`, magnetError)
              detail.magnets = []
            }

            // 请求下载图片
            ElMessage.info(`下载影片 ${movie.id} 的图片资源`)
            await downloadMovieImages({
              movieId: movie.id,
              coverImage: detail.img,
              sampleImages: detail.samples ? detail.samples.map(s => s.img) : [],
              starImages: detail.stars ? detail.stars.map(s => ({ id: s.id, image: s.img })) : []
            })

            detailedMovies.push(detail)
          } catch (error) {
            console.error(`获取影片 ${movie.id} 详情失败:`, error)
            ElMessage.error(`获取影片 ${movie.id} 详情失败`)
          }
        }
        
        // 保存到数据库
        ElMessage.info('正在保存数据到数据库')
        const response = await saveMovies({ 
          movies: detailedMovies,
          downloadImages: true // 告诉后端需要处理图片
        })
        
        // 后端返回的是异步任务状态，不是结果数组
        if (response && response.taskId) {
          ElMessage.success(`成功提交导入任务，共${response.totalMovies}部影片，正在后台处理...`)
          
          // 开始轮询导入状态
          startPollingStatus()
          
          importedMovies.value = [{
            id: 'task-' + response.taskId,
            title: `选中影片导入任务`,
            status: 'success',
            message: `已提交${response.totalMovies}部影片的导入任务`
          }]
        } else {
          ElMessage.warning('提交导入任务成功，但未收到任务ID')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败: ' + (error.message || '未知错误'))
      } finally {
        importing.value = false
      }
    }
    
    // 导入单个影片
    const handleImportSingle = async () => {
      if (!movieDetail.value) {
        ElMessage.warning('影片详情不存在')
        return
      }
      
      importing.value = true
      
      try {
        const movie = movieDetail.value

        // 获取磁力链接
        ElMessage.info(`正在获取影片 ${movie.id} 磁力链接`)
        try {
          const magnetsResponse = await fetch(`http://localhost:3000/api/magnets/${movie.id}?gid=${movie.gid}&uc=${movie.uc}`)
          if (magnetsResponse.ok) {
            const magnets = await magnetsResponse.json()
            movie.magnets = magnets || []
            console.log(`影片 ${movie.id} 获取到 ${movie.magnets.length} 个磁力链接`)
          } else {
            console.warn(`获取影片 ${movie.id} 磁力链接失败`)
            movie.magnets = []
          }
        } catch (magnetError) {
          console.error(`获取影片 ${movie.id} 磁力链接出错:`, magnetError)
          movie.magnets = []
        }

        // 请求下载图片
        ElMessage.info(`下载影片 ${movie.id} 的图片资源`)
        await downloadMovieImages({
          movieId: movie.id,
          coverImage: movie.img,
          sampleImages: movie.samples ? movie.samples.map(s => s.img) : [],
          starImages: movie.stars ? movie.stars.map(s => ({ id: s.id, image: s.img })) : []
        })

        // 保存到数据库
        ElMessage.info('正在保存数据到数据库')
        const response = await saveMovies({
          movies: [movie],
          downloadImages: true // 告诉后端需要处理图片
        })
        
        // 后端返回的是异步任务状态，不是结果数组
        if (response && response.taskId) {
          ElMessage.success(`成功提交影片 ${movie.id} 的导入任务，正在后台处理...`)
          
          // 开始轮询导入状态
          startPollingStatus()
          
          importedMovies.value = [{
            id: 'task-' + response.taskId,
            title: movie.title || movie.id,
            status: 'success',
            message: `已提交导入任务`
          }]
          
          dialogVisible.value = false
        } else {
          ElMessage.warning('提交导入任务成功，但未收到任务ID')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败: ' + (error.message || '未知错误'))
      } finally {
        importing.value = false
      }
    }

    // 更新发行日期
    const handleUpdateReleaseDate = async () => {
      try {
        const confirm = await ElMessageBox.confirm(
          '此操作将扫描所有有磁力链接的影片，将磁力链接中最早的分享日期设置为作品发行日期。是否继续？',
          '确认更新发行日期',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        if (confirm !== 'confirm') {
          return
        }
        
        updatingReleaseDate.value = true
        ElMessage.info('正在更新发行日期，请稍候...')
        
        const response = await fetch('/api/javbus-admin/update-release-date', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const result = await response.json()
        
        if (result.success) {
          ElMessage.success(`更新完成！处理了 ${result.data.processed} 部影片，成功更新 ${result.data.updated} 部`)
          
          // 显示详细统计
          ElMessageBox.alert(
            `处理影片总数: ${result.data.processed}\n成功更新: ${result.data.updated}\n跳过: ${result.data.skipped}\n错误: ${result.data.errors}`,
            '更新统计',
            {
              confirmButtonText: '确定',
              type: 'info'
            }
          )
        } else {
          throw new Error(result.message || '更新失败')
        }
        
      } catch (error) {
        console.error('更新发行日期失败:', error)
        ElMessage.error('更新发行日期失败: ' + (error.message || '未知错误'))
      } finally {
        updatingReleaseDate.value = false
      }
    }
    
    return {
      loading,
      importing,
      loadingDetail,
      updatingReleaseDate,
      dialogVisible,
      showPreview,
      moviesList,
      movieDetail,
      importForm,
      importedMovies,
      selectedMovies,
      importStatus,
      checkImportStatus,
      handleSearch,
      handlePageChange,
      handleSelectionChange,
      handleViewDetail,
      handleImport,
      handleImportSelected,
      handleImportSingle,
      handleUpdateReleaseDate
    }
  }
}
</script>

<style scoped>
.import-container {
  padding: 20px 0;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.movie-detail {
  padding: 20px;
}

.movie-detail-header {
  display: flex;
  margin-bottom: 20px;
}

.movie-poster {
  margin-right: 20px;
}

.movie-info {
  flex: 1;
}

.movie-meta p {
  margin: 8px 0;
}

.movie-section {
  margin-top: 20px;
}

.movie-stars {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.star-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
}

.star-name {
  margin-top: 10px;
  text-align: center;
  font-size: 14px;
}

.image-error {
  width: 75px;
  height: 100px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 30px 0;
  font-size: 14px;
}

.import-status {
  margin-bottom: 20px;
}

.import-stats {
  margin-top: 10px;
}

.error-message {
  margin-top: 10px;
  color: #f56c6c;
}
</style> 