package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"javflix-video-processor/internal/config"
	"javflix-video-processor/internal/handler"
	"javflix-video-processor/internal/service"
	"javflix-video-processor/internal/worker"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 设置Gin模式
	gin.SetMode(cfg.GinMode)

	// 初始化日志
	logger := logrus.New()
	level, err := logrus.ParseLevel(cfg.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)
	logger.SetFormatter(&logrus.JSONFormatter{})

	logger.WithFields(logrus.Fields{
		"port":         cfg.Port,
		"gin_mode":     cfg.GinMode,
		"log_level":    cfg.LogLevel,
		"cdn_provider": cfg.CDNProvider,
		"temp_dir":     cfg.TempDir,
	}).Info("启动Go视频处理服务器")

	// 初始化服务
	videoService := service.NewVideoService(cfg)
	defer videoService.Close()

	taskWorker := worker.NewTaskWorker(cfg, videoService)
	defer taskWorker.Stop()

	// 启动工作池
	go func() {
		logger.Info("启动任务工作池...")
		taskWorker.Start()
	}()

	// 设置Gin路由
	r := gin.New()
	r.Use(gin.Logger(), gin.Recovery())

	// 注册路由
	handler.RegisterRoutes(r, cfg, videoService, taskWorker)

	// 启动HTTP服务器
	srv := &http.Server{
		Addr:    ":" + cfg.Port,
		Handler: r,
	}

	go func() {
		logger.WithField("port", cfg.Port).Info("🚀 Go视频处理服务器启动")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("🛑 正在关闭服务器...")

	// 设置5秒的超时时间来关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal("服务器强制关闭:", err)
	}

	logger.Info("✅ 服务器已关闭")
}
