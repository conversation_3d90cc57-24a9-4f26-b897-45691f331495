const express = require('express');
const cors = require('cors');
const path = require('path');
const { connectDB } = require('./config/db');
const errorHandler = require('./middleware/error-handler');

// 导入路由
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/userRoutes');
const userStatsRoutes = require('./routes/userStatsRoutes');
const videoRoutes = require('./routes/videoRoutes');
const videoStatsRoutes = require('./routes/videoStatsRoutes');
const commentsRoutes = require('./routes/commentsRoutes');
// const actressRoutes = require('./routes/actress.routes');
// const systemRoutes = require('./routes/system.routes');
// const taskRoutes = require('./routes/task.routes');
const javbusRoutes = require('./routes/javbusRoutes');
const javbusImportRoutes = require('./routes/javbusImportRoutes');
// const downloadRoutes = require('./routes/download.routes');
// const dashboardRoutes = require('./routes/dashboard.routes');
// const uploadRoutes = require('./routes/upload.routes');
const featuredVideosRoutes = require('./routes/featuredVideosRoutes');
const recentVideosRoutes = require('./routes/recentVideosRoutes');
const recommendationRoutes = require('./routes/recommendationRoutes');
const dbRoutes = require('./routes/dbRoutes');
const movieProcessingRoutes = require('./routes/movieProcessingRoutes');
const publishedVideoRoutes = require('./routes/publishedVideoRoutes');
const { smartCache } = require('./middleware/cacheMiddleware');
const { apiCompressionMiddleware, compressionStatsMiddleware } = require('./middleware/compressionMiddleware');

const app = express();

// 连接数据库
connectDB();

// 中间件
app.use(cors());
app.use(apiCompressionMiddleware); // 启用API响应压缩
app.use(compressionStatsMiddleware); // 压缩统计
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
app.use('/images', express.static(path.join(__dirname, '../images')));

// 基础路由
app.get('/', (req, res) => {
  res.json({ message: 'JAVFLIX API服务运行中' });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/users/stats', userStatsRoutes);

// 查询路由 - 确保特定路径在通配符之前
// 对于videoRoutes，我们已经调整了路由顺序，确保/search在/:id之前
app.use('/api/videos', smartCache, videoRoutes);

// 视频统计路由 - 融合API
app.use('/api/video-stats', videoStatsRoutes);

// 评论路由
app.use('/api/comments', commentsRoutes);

// 其他路由
// app.use('/api/actresses', actressRoutes);
// app.use('/api/system', systemRoutes);
// app.use('/api/tasks', taskRoutes);
app.use('/api/javbus', javbusRoutes);
app.use('/api/javbus-admin', javbusImportRoutes);
app.use('/api/featured', featuredVideosRoutes);
app.use('/api/recent', recentVideosRoutes);
app.use('/api/recommendations', recommendationRoutes);
// app.use('/api/downloads', downloadRoutes);
// app.use('/api/dashboard', dashboardRoutes);
// app.use('/api/upload/image', uploadRoutes);

// 数据库API路由
app.use('/api/db', smartCache, dbRoutes);

// 影片处理管理路由
app.use('/api/movie-processing', movieProcessingRoutes);

// 已发布影片路由（用户前端）
app.use('/api/published-videos', smartCache, publishedVideoRoutes);

// 热门视频API - 基于缓存优化
app.get('/api/popular-videos', smartCache, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;
    const pool = req.app.get('pool');
    
    const result = await pool.query(`
      SELECT 
        m.id, 
        m.movie_id AS code, 
        m.title, 
        m.image_url AS "imageUrl", 
        m.release_date AS "releaseDate",
        m.view_count AS views
      FROM 
        movies m
      ORDER BY 
        m.view_count DESC NULLS LAST, m.id DESC
      LIMIT $1
    `, [limit]);
    
    res.json({
      success: true,
      data: result.rows,
      message: '热门视频获取成功'
    });
  } catch (error) {
    console.error('获取热门视频失败:', error);
    res.status(500).json({
      success: false,
      message: '获取热门视频失败',
      error: error.message
    });
  }
});

// 用户统计API - Redis统计系统
app.get('/api/user-stats', smartCache, async (req, res) => {
  try {
    const userId = req.query.userId;
    const pool = req.app.get('pool');
    
    // 基础统计
    const stats = {
      totalUsers: 0,
      totalMovies: 0,
      totalViews: 0,
      userSpecific: null
    };
    
    // 获取总用户数
    const userCountResult = await pool.query('SELECT COUNT(*) FROM users');
    stats.totalUsers = parseInt(userCountResult.rows[0].count);
    
    // 获取总电影数
    const movieCountResult = await pool.query('SELECT COUNT(*) FROM movies');
    stats.totalMovies = parseInt(movieCountResult.rows[0].count);
    
    // 获取总观看数
    const viewCountResult = await pool.query('SELECT SUM(view_count) FROM movies');
    stats.totalViews = parseInt(viewCountResult.rows[0].sum) || 0;
    
    // 如果提供了用户ID，获取用户特定统计
    if (userId) {
      try {
        const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [userId]);
        if (userResult.rows.length > 0) {
          const user = userResult.rows[0];
          
          // 获取用户喜欢的视频数量
          const likesResult = await pool.query('SELECT COUNT(*) FROM user_likes WHERE user_id = $1', [userId]);
          
          stats.userSpecific = {
            id: user.id,
            username: user.username,
            email: user.email,
            totalLikes: parseInt(likesResult.rows[0].count),
            joinDate: user.created_at
          };
        }
      } catch (userError) {
        console.log('获取用户特定统计失败:', userError.message);
      }
    }
    
    res.json({
      success: true,
      data: stats,
      message: '用户统计获取成功'
    });
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计失败',
      error: error.message
    });
  }
});

// 错误处理
app.use(errorHandler);

module.exports = app; 