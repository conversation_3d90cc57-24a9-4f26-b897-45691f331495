<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试 - 视频处理监控</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 5px solid #007bff;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.connected {
            background: #28a745;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .messages-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #28a745;
        }
        
        .messages {
            height: 400px;
            overflow-y: auto;
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        
        .message.progress {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .message.completed {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .message.failed {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .message.started {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
        
        .timestamp {
            color: #6c757d;
            font-size: 11px;
        }
        
        .message-type {
            font-weight: bold;
            color: #495057;
        }
        
        .task-info {
            margin-top: 5px;
            font-size: 12px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-top: 4px solid #007bff;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 视频处理监控</h1>
            <p>实时监控磁力下载和视频处理进度</p>
        </div>
        
        <div class="content">
            <div class="status-section">
                <h3>连接状态</h3>
                <div class="connection-status">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <span id="connectionStatus">未连接</span>
                </div>
                
                <div class="controls">
                    <button onclick="connect()" id="connectBtn">连接</button>
                    <button onclick="disconnect()" id="disconnectBtn" disabled>断开</button>
                    <button onclick="clearMessages()">清空消息</button>
                    <button onclick="sendTestMessage()">发送测试消息</button>
                </div>
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalMessages">0</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="progressMessages">0</div>
                    <div class="stat-label">进度消息</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completedTasks">0</div>
                    <div class="stat-label">完成任务</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTasks">0</div>
                    <div class="stat-label">失败任务</div>
                </div>
            </div>
            
            <div class="messages-section">
                <h3>实时消息</h3>
                <div class="messages" id="messages"></div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        let progressCount = 0;
        let completedCount = 0;
        let failedCount = 0;
        
        function connect() {
            const wsUrl = 'ws://localhost:8081/ws';
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                updateConnectionStatus(true);
                addMessage('system', '✅ WebSocket连接已建立', null);
            };
            
            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (error) {
                    addMessage('error', '❌ 解析消息失败: ' + error.message, null);
                }
            };
            
            ws.onclose = function(event) {
                updateConnectionStatus(false);
                addMessage('system', '❌ WebSocket连接已关闭', null);
            };
            
            ws.onerror = function(error) {
                addMessage('error', '❌ WebSocket错误: ' + error.message, null);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('statusIndicator');
            const status = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                indicator.classList.add('connected');
                status.textContent = '已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                indicator.classList.remove('connected');
                status.textContent = '未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }
        
        function handleMessage(message) {
            messageCount++;
            updateStats();
            
            switch (message.type) {
                case 'video-task-progress':
                    progressCount++;
                    addMessage('progress', '📊 任务进度更新', message.data);
                    break;
                case 'video-task-completed':
                    completedCount++;
                    addMessage('completed', '✅ 任务完成', message.data);
                    break;
                case 'video-task-failed':
                    failedCount++;
                    addMessage('failed', '❌ 任务失败', message.data);
                    break;
                case 'video-task-started':
                    addMessage('started', '🚀 任务开始', message.data);
                    break;
                default:
                    addMessage('info', '📝 未知消息类型: ' + message.type, message.data);
            }
        }
        
        function addMessage(type, text, data) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            let content = `<div class="timestamp">${timestamp}</div>`;
            content += `<div class="message-type">${text}</div>`;
            
            if (data) {
                content += '<div class="task-info">';
                if (data.taskUuid) content += `任务ID: ${data.taskUuid.slice(0, 8)}...<br>`;
                if (data.movieId) content += `影片ID: ${data.movieId}<br>`;
                if (data.progress !== undefined) content += `进度: ${data.progress}%<br>`;
                if (data.status) content += `状态: ${data.status}<br>`;
                if (data.message) content += `消息: ${data.message}<br>`;
                if (data.error) content += `错误: ${data.error}<br>`;
                content += '</div>';
            }
            
            messageDiv.innerHTML = content;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 0;
            progressCount = 0;
            completedCount = 0;
            failedCount = 0;
            updateStats();
        }
        
        function updateStats() {
            document.getElementById('totalMessages').textContent = messageCount;
            document.getElementById('progressMessages').textContent = progressCount;
            document.getElementById('completedTasks').textContent = completedCount;
            document.getElementById('failedTasks').textContent = failedCount;
        }
        
        function sendTestMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testMessage = {
                    type: 'test',
                    data: {
                        message: 'This is a test message',
                        timestamp: new Date().toISOString()
                    }
                };
                ws.send(JSON.stringify(testMessage));
                addMessage('info', '📤 发送测试消息', testMessage.data);
            } else {
                addMessage('error', '❌ WebSocket未连接', null);
            }
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            addMessage('system', '🌟 页面已加载，点击连接按钮开始监控', null);
        };
        
        // 页面卸载时断开连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>
